# =============================================================================
# ATMA Backend - Shared Environment Configuration
# =============================================================================
# This file contains shared configuration for all microservices
# Each service will load this file first, then override with service-specific .env
# =============================================================================

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================
NODE_ENV=development

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# PostgreSQL Database Configuration (shared across all services)
DB_HOST=localhost
DB_PORT=5432
DB_NAME=atma_db
DB_USER=postgres
DB_PASSWORD=password
DB_DIALECT=postgres

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
# JWT Secret (MUST be the same across all services for token verification)
JWT_SECRET=atma_secure_jwt_secret_key_f8a5b3c7d9e1f2a3b5c7d9e1f2a3b5c7
JWT_EXPIRES_IN=7d

# Internal Service Communication Key
INTERNAL_SERVICE_KEY=internal_service_secret_key_change_in_production

# Bcrypt Configuration
BCRYPT_ROUNDS=12

# =============================================================================
# RABBITMQ CONFIGURATION
# =============================================================================
# Message Queue Configuration (shared across services)
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
EXCHANGE_NAME=atma_exchange

# Queue Configuration
QUEUE_DURABLE=true
MESSAGE_PERSISTENT=true

# =============================================================================
# SERVICE URLS
# =============================================================================
# Internal Service Communication URLs
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3005

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
# Cross-Origin Resource Sharing Configuration
CORS_ORIGIN=http://localhost:3000,http://localhost:3001

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
# Logging Level (info, debug, warn, error)
LOG_LEVEL=info

# =============================================================================
# RATE LIMITING CONFIGURATION
# =============================================================================
# Rate limiting settings (primarily for API Gateway)
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# =============================================================================
# PAGINATION CONFIGURATION
# =============================================================================
# Default pagination settings
DEFAULT_PAGE_SIZE=10
MAX_PAGE_SIZE=100

# =============================================================================
# TOKEN CONFIGURATION
# =============================================================================
# Default token balance for new users
DEFAULT_TOKEN_BALANCE=5
ANALYSIS_TOKEN_COST=1

# =============================================================================
# WEBSOCKET CONFIGURATION
# =============================================================================
# WebSocket settings for notification service
WEBSOCKET_CORS_ORIGIN=http://localhost:3000,http://localhost:3001
WEBSOCKET_PING_TIMEOUT=60000
WEBSOCKET_PING_INTERVAL=25000
WEBSOCKET_MAX_CONNECTIONS=1000

# =============================================================================
# WORKER CONFIGURATION
# =============================================================================
# Analysis worker settings
WORKER_CONCURRENCY=3
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=300000
HEARTBEAT_INTERVAL=30000

# =============================================================================
# AI CONFIGURATION
# =============================================================================
# Google Generative AI Configuration
GOOGLE_AI_MODEL=gemini-1.5-pro
AI_TEMPERATURE=0.7
AI_MAX_TOKENS=4096

# =============================================================================
# NOTES
# =============================================================================
# 1. This file contains shared configuration for all microservices
# 2. Service-specific configurations should be in individual .env files
# 3. Service-specific .env files will override values from this shared file
# 4. Never commit sensitive values like API keys to version control
# 5. In production, use proper secret management systems
# =============================================================================