/**
 * Notification Service - Main Application
 * WebSocket server for real-time notifications
 */

const express = require('express');
const http = require('http');
const socketIo = require('socket.io');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');

// Load shared environment variables first, then service-specific ones
require('dotenv').config({ path: path.join(__dirname, '../../.env.shared') });
require('dotenv').config();

// Import utilities and middleware
const logger = require('./utils/logger');
const { errorHandler, notFoundHandler } = require('./middleware/errorHandler');

// Create Express app and HTTP server
const app = express();
const server = http.createServer(app);
const PORT = process.env.PORT || 3005;

// CORS configuration
const corsOptions = {
  origin: process.env.WEBSOCKET_CORS_ORIGIN ?
    process.env.WEBSOCKET_CORS_ORIGIN.split(',') :
    ['http://localhost:3000', 'http://localhost:3001'],
  credentials: true,
  optionsSuccessStatus: 200
};

app.use(cors(corsOptions));

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Logging middleware
if (process.env.NODE_ENV === 'development') {
  app.use(morgan('dev'));
} else {
  app.use(morgan('combined', {
    stream: {
      write: (message) => logger.info(message.trim())
    }
  }));
}

// Socket.IO configuration
const io = socketIo(server, {
  cors: corsOptions,
  pingTimeout: parseInt(process.env.WEBSOCKET_PING_TIMEOUT || '60000'),
  pingInterval: parseInt(process.env.WEBSOCKET_PING_INTERVAL || '25000'),
  maxHttpBufferSize: 1e6, // 1MB
  transports: ['websocket', 'polling']
});

// Socket.IO authentication middleware
io.use((socket, next) => {
  const token = socket.handshake.auth.token;
  if (!token) {
    return next(new Error('Authentication error: No token provided'));
  }

  try {
    const jwt = require('jsonwebtoken');
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    socket.userId = decoded.id;
    socket.userEmail = decoded.email;
    next();
  } catch (err) {
    logger.error('Socket authentication failed', { error: err.message });
    next(new Error('Authentication error: Invalid token'));
  }
});

// Socket.IO connection handling
io.on('connection', (socket) => {
  logger.info('Client connected', {
    socketId: socket.id,
    userId: socket.userId,
    userEmail: socket.userEmail
  });

  // Join user to their personal room
  socket.join(`user_${socket.userId}`);

  // Handle disconnection
  socket.on('disconnect', (reason) => {
    logger.info('Client disconnected', {
      socketId: socket.id,
      userId: socket.userId,
      reason
    });
  });

  // Handle ping for connection monitoring
  socket.on('ping', () => {
    socket.emit('pong');
  });
});

// Routes
app.use('/health', require('./routes/health'));

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'ATMA Notification Service is running',
    version: '1.0.0',
    timestamp: new Date().toISOString(),
    connectedClients: io.sockets.sockets.size
  });
});

// 404 handler
app.use('*', notFoundHandler);

// Error handling middleware
app.use(errorHandler);

// Initialize services
const initializeServices = async () => {
  try {
    // Initialize RabbitMQ consumer for notifications
    const queueConsumer = require('./services/queueConsumer');
    await queueConsumer.initialize(io);

    logger.info('All services initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize services', { error: error.message });
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async () => {
  logger.info('Shutting down gracefully...');
  try {
    // Close Socket.IO server
    io.close();

    // Close HTTP server
    server.close();

    logger.info('All services closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown', { error: error.message });
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
if (process.env.NODE_ENV !== 'test') {
  initializeServices().then(() => {
    server.listen(PORT, () => {
      logger.info(`Notification Service running on port ${PORT}`, {
        port: PORT,
        environment: process.env.NODE_ENV || 'development',
        version: '1.0.0'
      });
    });
  }).catch((error) => {
    logger.error('Failed to start Notification Service', { error: error.message });
    process.exit(1);
  });
}

module.exports = { app, server, io };