const express = require('express');
const router = express.Router();
const logger = require('../utils/logger');

/**
 * GET /health
 * Basic health check endpoint
 */
router.get('/', async (req, res) => {
  try {
    const healthStatus = {
      status: 'healthy',
      timestamp: new Date().toISOString(),
      websocket: 'running',
      version: '1.0.0',
      service: 'notification-service'
    };

    // Log health check
    logger.info('Health check performed', {
      status: healthStatus.status
    });

    res.status(200).json(healthStatus);
  } catch (error) {
    logger.error('Health check failed', { error: error.message });

    res.status(503).json({
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      error: error.message,
      service: 'notification-service'
    });
  }
});

/**
 * GET /health/ready
 * Readiness probe
 */
router.get('/ready', async (req, res) => {
  try {
    // Check if service is ready to accept connections
    const isReady = true; // Add actual readiness checks here

    if (!isReady) {
      return res.status(503).json({
        status: 'not_ready',
        reason: 'Service not ready',
        timestamp: new Date().toISOString()
      });
    }

    return res.status(200).json({
      status: 'ready',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('Readiness check error', { error: error.message });

    return res.status(503).json({
      status: 'not_ready',
      reason: error.message,
      timestamp: new Date().toISOString()
    });
  }
});

/**
 * GET /health/live
 * Liveness probe
 */
router.get('/live', (req, res) => {
  res.status(200).json({
    status: 'alive',
    timestamp: new Date().toISOString()
  });
});

module.exports = router;