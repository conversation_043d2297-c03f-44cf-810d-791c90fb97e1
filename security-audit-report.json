{"timestamp": "2025-07-15T11:26:55.695Z", "summary": {"totalServices": 6, "secureServices": 6, "vulnerableServices": 0, "totalVulnerabilities": 0}, "results": [{"service": "api-gateway", "status": "success", "vulnerabilities": 0, "message": "No vulnerabilities found"}, {"service": "auth-service", "status": "success", "vulnerabilities": 0, "message": "No vulnerabilities found"}, {"service": "archive-service", "status": "success", "vulnerabilities": 0, "message": "No vulnerabilities found"}, {"service": "assessment-service", "status": "success", "vulnerabilities": 0, "message": "No vulnerabilities found"}, {"service": "analysis-worker", "status": "success", "vulnerabilities": 0, "message": "No vulnerabilities found"}, {"service": "notification-service", "status": "success", "vulnerabilities": 0, "severityBreakdown": {}, "output": "npm error code ENOLOCK\nnpm error audit This command requires an existing lockfile.\nnpm error audit Try creating one first with: npm i --package-lock-only\nnpm error audit Original error: loadVirtual requires existing shrinkwrap file\nnpm error A complete log of this run can be found in: C:\\Users\\<USER>\\AppData\\Local\\npm-cache\\_logs\\2025-07-15T11_26_55_390Z-debug-0.log\n"}]}