/**
 * RabbitMQ Queue Consumer for Notification Service
 * Listens for analysis completion events and sends WebSocket notifications
 */

const amqp = require('amqplib');
const logger = require('../utils/logger');

// RabbitMQ configuration
const config = {
  url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
  exchange: process.env.EXCHANGE_NAME || 'atma_exchange',
  queue: process.env.NOTIFICATION_QUEUE || 'analysis_notifications',
  routingKey: process.env.ROUTING_KEY || 'notification.send'
};

// Connection and channel variables
let connection = null;
let channel = null;
let io = null; // Socket.IO instance

/**
 * Initialize RabbitMQ connection and setup queue
 * @param {Object} socketIo - Socket.IO instance
 */
const initialize = async (socketIo) => {
  try {
    io = socketIo;

    logger.info('Connecting to RabbitMQ', { url: config.url });

    // Create connection
    connection = await amqp.connect(config.url);

    // Handle connection events
    connection.on('error', (err) => {
      logger.error('RabbitMQ connection error', { error: err.message });
    });

    connection.on('close', () => {
      logger.warn('RabbitMQ connection closed');
    });

    // Create channel
    channel = await connection.createChannel();

    // Assert exchange
    await channel.assertExchange(config.exchange, 'topic', { durable: true });

    // Assert queue
    await channel.assertQueue(config.queue, { durable: true });

    // Bind queue to exchange
    await channel.bindQueue(config.queue, config.exchange, config.routingKey);

    // Set prefetch count
    await channel.prefetch(1);

    logger.info('RabbitMQ initialized successfully', {
      exchange: config.exchange,
      queue: config.queue,
      routingKey: config.routingKey
    });

    // Start consuming messages
    await startConsuming();

  } catch (error) {
    logger.error('Failed to initialize RabbitMQ', { error: error.message });
    throw error;
  }
};

/**
 * Start consuming messages from the queue
 */
const startConsuming = async () => {
  try {
    await channel.consume(config.queue, async (message) => {
      if (message) {
        try {
          const content = JSON.parse(message.content.toString());
          logger.info('Received notification message', {
            userId: content.userId,
            type: content.type
          });

          // Process the notification
          await processNotification(content);

          // Acknowledge the message
          channel.ack(message);

        } catch (error) {
          logger.error('Error processing notification message', {
            error: error.message,
            message: message.content.toString()
          });

          // Reject the message and don't requeue
          channel.nack(message, false, false);
        }
      }
    });

    logger.info('Started consuming notification messages');

  } catch (error) {
    logger.error('Failed to start consuming messages', { error: error.message });
    throw error;
  }
};

/**
 * Process notification and send to user via WebSocket
 * @param {Object} notification - Notification data
 */
const processNotification = async (notification) => {
  try {
    const { userId, type, data } = notification;

    if (!userId) {
      logger.warn('Notification missing userId', { notification });
      return;
    }

    // Send notification to user's room
    const userRoom = `user_${userId}`;
    const connectedSockets = io.sockets.adapter.rooms.get(userRoom);

    if (connectedSockets && connectedSockets.size > 0) {
      io.to(userRoom).emit(type, data);

      logger.info('Notification sent successfully', {
        userId,
        type,
        connectedClients: connectedSockets.size
      });
    } else {
      logger.info('User not connected, notification not sent', {
        userId,
        type
      });
    }

  } catch (error) {
    logger.error('Error processing notification', {
      error: error.message,
      notification
    });
    throw error;
  }
};

/**
 * Close RabbitMQ connection
 */
const close = async () => {
  try {
    if (channel) {
      await channel.close();
      channel = null;
    }

    if (connection) {
      await connection.close();
      connection = null;
    }

    logger.info('RabbitMQ connection closed');

  } catch (error) {
    logger.error('Error closing RabbitMQ connection', { error: error.message });
    throw error;
  }
};

/**
 * Check if RabbitMQ is healthy
 * @returns {boolean} - Health status
 */
const isHealthy = () => {
  return connection && !connection.connection.stream.destroyed && channel;
};

module.exports = {
  initialize,
  startConsuming,
  close,
  isHealthy
};