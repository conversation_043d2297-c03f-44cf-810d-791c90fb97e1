const express = require('express');
const cors = require('cors');
const morgan = require('morgan');
const path = require('path');

// Load shared environment variables first, then service-specific ones
require('dotenv').config({ path: path.join(__dirname, '../../.env.shared') });
require('dotenv').config();

// Import routes
const assessmentRoutes = require('./routes/assessments');
const healthRoutes = require('./routes/health');

// Import utilities
const logger = require('./utils/logger');
const { errorHandler } = require('./middleware/errorHandler');

// Import services
const queueService = require('./services/queueService');

// Create Express app
const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(cors());
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));
app.use(morgan('combined', {
  stream: {
    write: (message) => logger.info(message.trim())
  }
}));

// Routes
app.use('/assessments', assessmentRoutes);
app.use('/health', healthRoutes);

// Root endpoint
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'ATMA Assessment Service is running',
    version: '1.0.0',
    timestamp: new Date().toISOString()
  });
});

// 404 handler
app.use('*', (req, res) => {
  res.status(404).json({
    success: false,
    error: {
      code: 'NOT_FOUND',
      message: `Route ${req.method} ${req.originalUrl} not found`
    }
  });
});

// Error handling middleware
app.use(errorHandler);

// Initialize services
const initializeServices = async() => {
  try {
    await queueService.initialize();
    logger.info('All services initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize services', { error: error.message });
    process.exit(1);
  }
};

// Graceful shutdown
const gracefulShutdown = async() => {
  logger.info('Shutting down gracefully...');
  try {
    await queueService.close();
    logger.info('All services closed successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error during shutdown', { error: error.message });
    process.exit(1);
  }
};

process.on('SIGTERM', gracefulShutdown);
process.on('SIGINT', gracefulShutdown);

// Start server
if (process.env.NODE_ENV !== 'test') {
  initializeServices().then(() => {
    app.listen(PORT, () => {
      logger.info(`Assessment Service running on port ${PORT}`);
      logger.info(`Environment: ${process.env.NODE_ENV || 'development'}`);
    });
  });
}

module.exports = app;
